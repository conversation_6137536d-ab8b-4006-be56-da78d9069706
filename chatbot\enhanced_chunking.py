import mysql.connector
import hashlib
import fitz  # PyMuPDF
import spacy
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
import logging

# Set up detailed logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - [CHUNKING] %(message)s",
    handlers=[
        logging.FileHandler('enhanced_chunking.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Load spaCy model once
nlp = spacy.load("en_core_web_sm")

# MySQL connection setup
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': 'vishal2004',
    'database': 'rough'
}

# ---------- MySQL Helpers ----------

def get_db_connection():
    """Get a fresh database connection."""
    return mysql.connector.connect(**db_config)

def fetch_pdf_files_from_db():
    """Fetch original PDF files stored as BLOBs from MySQL."""
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT id, file_name, file_data, camera_type FROM pdf_files")
    results = cursor.fetchall()
    cursor.close()
    conn.close()
    return results

def fetch_summary_pdfs_from_db():
    """Fetch summary PDF files generated by img2.py."""
    conn = get_db_connection()
    cursor = conn.cursor()
    # Look for summary PDFs (those ending with _summary.pdf)
    cursor.execute("SELECT id, file_name, file_data FROM pdf_files WHERE file_name LIKE '%_summary.pdf'")
    results = cursor.fetchall()
    cursor.close()
    conn.close()
    return results

def fetch_existing_hashes():
    """Fetch file_hashes already processed and stored in the database."""
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT DISTINCT file_hash FROM pdf_chunks")
    results = set(row[0] for row in cursor.fetchall())
    cursor.close()
    conn.close()
    return results

def insert_chunks_to_db(chunks):
    """Insert new PDF chunks into the MySQL database."""
    if not chunks:
        logger.info("No chunks to insert.")
        return
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    for chunk in chunks:
        try:
            cursor.execute("""
                INSERT INTO pdf_chunks
                (source_file, chunk_number, content, file_hash, last_modified, chunked, vector_embedded, camera_type, chunk_type)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                chunk['source_file'],
                int(chunk['chunk_number']),
                chunk['content'],
                chunk['file_hash'],
                chunk['last_modified'],
                chunk['status']['chunked'],
                chunk['status']['vector_embedded'],
                chunk.get('camera_type'),
                chunk.get('chunk_type', 'original')  # 'original' or 'summary'
            ))
            
            logger.debug(f"Inserted chunk {chunk['chunk_number']} from {chunk['source_file']}")
            
        except Exception as e:
            logger.error(f"Error inserting chunk {chunk['chunk_number']} from {chunk['source_file']}: {e}")
            
    conn.commit()
    cursor.close()
    conn.close()
    logger.info(f"Successfully inserted {len(chunks)} chunks to database.")

# ---------- Text Processing ----------

def calculate_sha256_from_bytes(data):
    """Calculate SHA256 hash from binary data."""
    return hashlib.sha256(data).hexdigest()

def load_pdf_from_bytes(pdf_bytes):
    """Extract text from PDF bytes using PyMuPDF."""
    try:
        doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        text = ""
        for page_num in range(len(doc)):
            page = doc[page_num]
            text += page.get_text()
        doc.close()
        return text.strip()
    except Exception as e:
        logger.error(f"Error extracting text from PDF: {e}")
        return ""

def split_into_paragraphs_with_spacy(text):
    """Split text into paragraphs using spaCy."""
    if not text:
        return []
    
    doc = nlp(text)
    paragraphs = []
    current_paragraph = ""
    
    for sent in doc.sents:
        sent_text = sent.text.strip()
        if sent_text:
            if current_paragraph:
                current_paragraph += " " + sent_text
            else:
                current_paragraph = sent_text
            
            # End paragraph on double newline or if it gets too long
            if "\n\n" in sent_text or len(current_paragraph) > 800:
                if current_paragraph.strip():
                    paragraphs.append(current_paragraph.strip())
                current_paragraph = ""
    
    # Add the last paragraph if it exists
    if current_paragraph.strip():
        paragraphs.append(current_paragraph.strip())
    
    return paragraphs

def dynamic_chunking(paragraphs, max_chunk_size=1000, overlap_size=50):
    """Combine paragraphs into chunks with overlap."""
    if not paragraphs:
        return []
    
    chunks = []
    current_chunk = ""
    
    for paragraph in paragraphs:
        # If adding this paragraph would exceed max size, save current chunk
        if current_chunk and len(current_chunk) + len(paragraph) + 1 > max_chunk_size:
            chunks.append(current_chunk.strip())
            
            # Create overlap by taking the last part of the current chunk
            words = current_chunk.split()
            if len(words) > overlap_size:
                overlap_text = " ".join(words[-overlap_size:])
                current_chunk = overlap_text + " " + paragraph
            else:
                current_chunk = paragraph
        else:
            # Add paragraph to current chunk
            if current_chunk:
                current_chunk += " " + paragraph
            else:
                current_chunk = paragraph
    
    # Add the final chunk
    if current_chunk.strip():
        chunks.append(current_chunk.strip())
    
    return chunks

# ---------- Processing Functions ----------

def process_original_pdfs():
    """Process original PDF files for chunking."""
    logger.info("🔍 Processing original PDF files...")
    
    pdf_records = fetch_pdf_files_from_db()
    if not pdf_records:
        logger.info("No original PDF records found in database.")
        return []
    
    logger.info(f"Found {len(pdf_records)} original PDF files to process.")
    existing_hashes = fetch_existing_hashes()
    new_chunks = []
    
    def process_record(record):
        id_, file_name, file_data, camera_type = record
        
        # Skip summary PDFs in this function
        if file_name.endswith('_summary.pdf'):
            return []
        
        file_hash = calculate_sha256_from_bytes(file_data)
        last_modified_str = datetime.now().isoformat()
        
        if file_hash in existing_hashes:
            logger.info(f"⏩ Skipping {file_name} (already processed)")
            return []
        
        logger.info(f"📄 Processing original PDF: {file_name} (Camera Type: {camera_type})")
        text = load_pdf_from_bytes(file_data)
        
        if not text:
            logger.warning(f"⚠️ No text extracted from {file_name}")
            return []
        
        logger.info(f"📝 Extracted {len(text)} characters from {file_name}")
        
        paragraphs = split_into_paragraphs_with_spacy(text)
        logger.info(f"📋 Split into {len(paragraphs)} paragraphs")
        
        chunks_text = dynamic_chunking(paragraphs)
        logger.info(f"🔗 Created {len(chunks_text)} chunks for {file_name}")
        
        chunks = []
        for idx, chunk in enumerate(chunks_text):
            chunk_data = {
                "source_file": file_name,
                "chunk_number": f"{idx+1}",
                "content": chunk,
                "file_hash": file_hash,
                "last_modified": last_modified_str,
                "camera_type": camera_type,
                "chunk_type": "original",
                "status": {
                    "chunked": True,
                    "vector_embedded": False
                }
            }
            chunks.append(chunk_data)
            
            # Log chunk details
            logger.debug(f"Chunk {idx+1} from {file_name} [Tokens: {len(chunk.split())}]: {chunk[:100]}...")
        
        return chunks
    
    # Process records with threading
    with ThreadPoolExecutor(max_workers=4) as executor:
        results = executor.map(process_record, pdf_records)
        for chunks in results:
            new_chunks.extend(chunks)
    
    logger.info(f"✅ Processed {len(new_chunks)} chunks from original PDFs")
    return new_chunks

def process_summary_pdfs():
    """Process summary PDF files generated by img2.py."""
    logger.info("🔍 Processing summary PDF files...")
    
    summary_records = fetch_summary_pdfs_from_db()
    if not summary_records:
        logger.info("No summary PDF records found in database.")
        return []
    
    logger.info(f"Found {len(summary_records)} summary PDF files to process.")
    existing_hashes = fetch_existing_hashes()
    new_chunks = []
    
    def process_summary_record(record):
        id_, file_name, file_data = record
        file_hash = calculate_sha256_from_bytes(file_data)
        last_modified_str = datetime.now().isoformat()
        
        if file_hash in existing_hashes:
            logger.info(f"⏩ Skipping {file_name} (already processed)")
            return []
        
        logger.info(f"📄 Processing summary PDF: {file_name}")
        text = load_pdf_from_bytes(file_data)
        
        if not text:
            logger.warning(f"⚠️ No text extracted from summary PDF {file_name}")
            return []
        
        logger.info(f"📝 Extracted {len(text)} characters from summary PDF {file_name}")
        
        # For summary PDFs, we might want smaller chunks since they contain concentrated information
        paragraphs = split_into_paragraphs_with_spacy(text)
        logger.info(f"📋 Split into {len(paragraphs)} paragraphs")
        
        chunks_text = dynamic_chunking(paragraphs, max_chunk_size=800, overlap_size=30)
        logger.info(f"🔗 Created {len(chunks_text)} chunks for summary PDF {file_name}")
        
        # Extract camera type from original filename
        original_name = file_name.replace('_summary.pdf', '.pdf')
        camera_type = get_camera_type_for_file(original_name)
        
        chunks = []
        for idx, chunk in enumerate(chunks_text):
            chunk_data = {
                "source_file": file_name,
                "chunk_number": f"{idx+1}",
                "content": chunk,
                "file_hash": file_hash,
                "last_modified": last_modified_str,
                "camera_type": camera_type,
                "chunk_type": "summary",
                "status": {
                    "chunked": True,
                    "vector_embedded": False
                }
            }
            chunks.append(chunk_data)
            
            # Log chunk details
            logger.debug(f"Summary chunk {idx+1} from {file_name} [Tokens: {len(chunk.split())}]: {chunk[:100]}...")
        
        return chunks
    
    # Process summary records
    with ThreadPoolExecutor(max_workers=4) as executor:
        results = executor.map(process_summary_record, summary_records)
        for chunks in results:
            new_chunks.extend(chunks)
    
    logger.info(f"✅ Processed {len(new_chunks)} chunks from summary PDFs")
    return new_chunks

def get_camera_type_for_file(filename):
    """Get camera type for a given filename from the original PDF records."""
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT camera_type FROM pdf_files WHERE file_name = %s", (filename,))
    result = cursor.fetchone()
    cursor.close()
    conn.close()
    return result[0] if result else None

# ---------- Main Processing Logic ----------

def process_all_pdfs():
    """Main function to process both original and summary PDFs."""
    logger.info("🚀 Starting enhanced chunking process...")
    
    # Process original PDFs first
    original_chunks = process_original_pdfs()
    
    # Process summary PDFs
    summary_chunks = process_summary_pdfs()
    
    # Combine all chunks
    all_chunks = original_chunks + summary_chunks
    
    if all_chunks:
        logger.info(f"💾 Inserting {len(all_chunks)} total chunks to database...")
        insert_chunks_to_db(all_chunks)
        
        # Log summary
        original_count = len(original_chunks)
        summary_count = len(summary_chunks)
        logger.info(f"📊 Chunking Summary:")
        logger.info(f"  📄 Original PDF chunks: {original_count}")
        logger.info(f"  📋 Summary PDF chunks: {summary_count}")
        logger.info(f"  🔗 Total chunks processed: {len(all_chunks)}")
    else:
        logger.info("ℹ️ No new chunks to process.")
    
    logger.info("✅ Enhanced chunking process completed!")

# ---------- Main Entry Point ----------

if __name__ == "__main__":
    process_all_pdfs()
