import fitz  # PyMuPDF
import openai
import pytesseract
from fpdf import FPDF
from io import BytesIO
from PIL import Image, ImageEnhance
import base64
import mysql.connector
from datetime import datetime
import os
import json
from django.conf import settings
import logging
try:
    from .openai_usage_tracker import track_openai_vision_completion
except ImportError:
    # Fallback for when running as standalone script
    def track_openai_vision_completion(model, messages, max_tokens, purpose):
        import openai
        return openai.ChatCompletion.create(
            model=model,
            messages=messages,
            max_tokens=max_tokens
        )

# Set up detailed logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - [IMG2] %(message)s",
    handlers=[
        logging.FileHandler('img2_processing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


# --- Config ---
openai.api_key = "********************************************************************************************************************************************************************"  # ✅ Use your actual key
pytesseract.pytesseract.tesseract_cmd = r"C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe"

db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': 'vishal2004',
    'database': 'rough'
}
def get_img2_prompt():
    import os

    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
    PROMPTS_JSON_PATH = r"D:\AI-Agent-Chatbot-main\chatbot\prompt.json"

# Then read the prompt JSON file from PROMPTS_JSON_PATH

    try:
        with open(PROMPTS_JSON_PATH, "r") as f:
            prompts = json.load(f)
        prompt_data = prompts.get("img2")
        if not prompt_data or not prompt_data.get("is_active"):
            return "You are analyzing a technical image. Provide a clear explanation."
        return prompt_data.get("template", "You are analyzing a technical image. Provide a clear explanation.")
    except Exception as e:
        print(f"Error loading prompt from JSON: {e}")
        return "You are analyzing a technical image. Provide a clear explanation."

# --- MySQL fetch ---
def fetch_unsummarized_pdf():
    """Fetch one unsummarized PDF from pdf_image_pages table."""
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor()
    cursor.execute("SELECT id, file_name, file_data FROM pdf_image_pages WHERE summarized = FALSE LIMIT 1")
    result = cursor.fetchone()
    cursor.close()
    conn.close()
    return result  # (id, file_name, file_data) or None

def fetch_all_unsummarized_pdfs():
    """Fetch all unsummarized PDFs from pdf_image_pages table."""
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor()
    cursor.execute("SELECT id, file_name, file_data FROM pdf_image_pages WHERE summarized = FALSE")
    results = cursor.fetchall()
    cursor.close()
    conn.close()
    return results

# --- Update summarized flag ---
def mark_as_summarized(pdf_id):
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor()
    cursor.execute("UPDATE pdf_image_pages SET summarized = TRUE WHERE id = %s", (pdf_id,))
    conn.commit()
    cursor.close()
    conn.close()

# --- Save summarized PDF to pdf_files ---
def save_summary_pdf_to_db(filename, pdf_data):
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor()
    last_modified = datetime.now()
    cursor.execute("""
        INSERT INTO pdf_files (file_name, file_data, last_modified)
        VALUES (%s, %s, %s)
    """, (filename, pdf_data, last_modified))
    conn.commit()
    cursor.close()
    conn.close()
    print(f"Summary PDF inserted into pdf_files as '{filename}'")

# --- Extract image pages ---
def extract_pages_with_images(pdf_bytes, filename=""):
    """Extract pages containing images from PDF bytes."""
    pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
    pages_data = []
    total_pages = len(pdf_doc)

    logger.info(f"🔍 Scanning {total_pages} pages in {filename} for images...")

    for page_num in range(total_pages):
        page = pdf_doc[page_num]
        images = page.get_images(full=True)

        if images:
            logger.info(f"📷 Found {len(images)} image(s) on page {page_num + 1}")
            pix = page.get_pixmap(dpi=300)
            img_bytes = BytesIO(pix.tobytes("png")).getvalue()
            pages_data.append({
                "page_number": page_num + 1,
                "image_bytes": img_bytes
            })
        else:
            logger.debug(f"⚪ No images found on page {page_num + 1}")

    pdf_doc.close()

    if pages_data:
        page_numbers = [p["page_number"] for p in pages_data]
        logger.info(f"✅ Image pages detected in {filename}: {page_numbers}")
    else:
        logger.warning(f"⚠️ No image pages found in {filename}")

    return pages_data

# --- OCR ---
def ocr_image_bytes(image_bytes, page_number=None, filename=""):
    """Perform OCR on image bytes with enhanced logging."""
    try:
        logger.debug(f"🔤 Starting OCR for page {page_number} of {filename}")
        img = Image.open(BytesIO(image_bytes)).convert("L")
        img = ImageEnhance.Contrast(img).enhance(2.0)
        ocr_text = pytesseract.image_to_string(img, config="--psm 6").strip()

        if ocr_text:
            logger.info(f"📝 OCR Text [Page {page_number}]: \"{ocr_text[:100]}{'...' if len(ocr_text) > 100 else ''}\"")
        else:
            logger.warning(f"⚠️ No OCR text extracted from page {page_number}")

        return ocr_text
    except Exception as e:
        logger.error(f"❌ OCR error on page {page_number}: {e}")
        return ""

# --- GPT Vision ---
def ask_gpt4v_with_image_and_ocr(image_bytes, ocr_text, page_number=None, filename=""):
    """Analyze image using GPT-4 Vision with OCR text context."""
    try:
        logger.info(f"🤖 Analyzing page {page_number} of {filename} with GPT-4 Vision...")

        image_base64 = base64.b64encode(image_bytes).decode('utf-8')
        image_url = f"data:image/png;base64,{image_base64}"

        ocr_instruction = f"OCR Text:\n{ocr_text}" if ocr_text else "No OCR text available. Focus on visual analysis."

        base_prompt = get_img2_prompt()
        prompt = f"{base_prompt}\n\n{ocr_instruction}"

        logger.debug(f"🔍 Sending image analysis request for page {page_number}")

        response = track_openai_vision_completion(
            model="gpt-4o",
            messages=[
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", "image_url": {"url": image_url}}
                    ]
                }
            ],
            max_tokens=700,
            purpose="image_analysis"
        )

        analysis_result = response.choices[0].message.content.strip()
        logger.info(f"✅ GPT-4 Vision analysis completed for page {page_number}")
        logger.debug(f"📋 Analysis result: {analysis_result[:150]}{'...' if len(analysis_result) > 150 else ''}")

        return analysis_result
    except Exception as e:
        logger.error(f"❌ GPT-4o Vision error for page {page_number}: {e}")
        return None

# --- Save PDF ---
def save_summaries_to_pdf(summaries):
    pdf = FPDF()
    pdf.set_auto_page_break(auto=True, margin=15)
    pdf.set_font("Arial", size=11)

    def clean(text):
        return text.encode("latin-1", "replace").decode("latin-1")

    for s in summaries:
        pdf.add_page()
        title = f"Page {s['page']} Summary:\n\n"
        pdf.multi_cell(0, 10, clean(title) + clean(s["summary"]))

    return pdf.output(dest='S').encode('latin-1')

# --- Enhanced Full Flow ---
def summarize_from_db():
    """Process all unsummarized PDFs sequentially with full debug logging."""
    logger.info("🚀 Starting image processing and OCR analysis...")

    # Fetch all unsummarized PDFs at once for better logging
    all_pdf_entries = fetch_all_unsummarized_pdfs()

    if not all_pdf_entries:
        logger.info("✅ All image PDFs have been summarized.")
        return

    logger.info(f"📊 Found {len(all_pdf_entries)} PDFs to process")

    processed_count = 0
    failed_count = 0

    for pdf_entry in all_pdf_entries:
        pdf_id, original_name, pdf_bytes = pdf_entry
        logger.info(f"📄 Processing PDF: {original_name} (ID: {pdf_id})")

        try:
            # Step 1: Extract image pages
            pages = extract_pages_with_images(pdf_bytes, original_name)
            summaries = []

            if not pages:
                logger.warning(f"⚠️ No image pages found in {original_name}")
                mark_as_summarized(pdf_id)
                continue

            # Step 2: Process each image page
            for page in pages:
                page_num = page['page_number']
                logger.info(f"🔍 Processing page {page_num} of {original_name}...")

                # OCR processing
                ocr_text = ocr_image_bytes(page["image_bytes"], page_num, original_name)

                # GPT Vision analysis
                summary = ask_gpt4v_with_image_and_ocr(
                    page["image_bytes"],
                    ocr_text,
                    page_num,
                    original_name
                )

                if summary:
                    summaries.append({
                        "page": page_num,
                        "summary": summary,
                        "ocr_text": ocr_text
                    })
                    logger.info(f"✅ Successfully processed page {page_num}")
                else:
                    logger.error(f"❌ Failed to generate summary for page {page_num}")

            # Step 3: Generate and save summary PDF
            if summaries:
                logger.info(f"📝 Generating summary PDF for {original_name}...")
                summarized_pdf_bytes = save_summaries_to_pdf(summaries)
                summary_filename = f"{original_name.rsplit('.', 1)[0]}_summary.pdf"

                save_summary_pdf_to_db(summary_filename, summarized_pdf_bytes)
                mark_as_summarized(pdf_id)

                processed_count += 1
                logger.info(f"✅ Completed summarization for: {original_name}")
                logger.info(f"📋 Generated {len(summaries)} page summaries")
            else:
                logger.warning(f"⚠️ No summaries generated for {original_name}")
                mark_as_summarized(pdf_id)
                failed_count += 1

        except Exception as e:
            logger.error(f"❌ Error processing {original_name}: {e}")
            failed_count += 1
            # Still mark as summarized to avoid reprocessing
            mark_as_summarized(pdf_id)

    # Final summary
    logger.info("🎉 Image processing completed!")
    logger.info(f"📊 Processing Summary:")
    logger.info(f"  ✅ Successfully processed: {processed_count}")
    logger.info(f"  ❌ Failed: {failed_count}")
    logger.info(f"  📄 Total PDFs: {len(all_pdf_entries)}")


# --- Run ---
if __name__ == "__main__":
    summarize_from_db()
