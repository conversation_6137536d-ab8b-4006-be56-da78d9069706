#!/usr/bin/env python3
"""
Check the status of the processing pipeline - PDFs, image pages, chunks, and embeddings
"""

import mysql.connector

# Database config
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': 'vishal2004',
    'database': 'rough'
}

def check_pipeline_status():
    """Check the status of all pipeline components."""
    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()
        
        print("🔍 PIPELINE STATUS CHECK")
        print("=" * 50)
        
        # 1. Check original PDFs
        cursor.execute("SELECT COUNT(*) FROM pdf_files WHERE file_name NOT LIKE '%_summary.pdf'")
        original_pdf_count = cursor.fetchone()[0]
        print(f"📄 Original PDFs in database: {original_pdf_count}")
        
        # 2. Check summary PDFs
        cursor.execute("SELECT COUNT(*) FROM pdf_files WHERE file_name LIKE '%_summary.pdf'")
        summary_pdf_count = cursor.fetchone()[0]
        print(f"📋 Summary PDFs in database: {summary_pdf_count}")
        
        # 3. Check image pages (unsummarized)
        cursor.execute("SELECT COUNT(*) FROM pdf_image_pages WHERE summarized = FALSE")
        unsummarized_count = cursor.fetchone()[0]
        print(f"🖼️ Unsummarized image pages: {unsummarized_count}")
        
        # 4. Check image pages (summarized)
        cursor.execute("SELECT COUNT(*) FROM pdf_image_pages WHERE summarized = TRUE")
        summarized_count = cursor.fetchone()[0]
        print(f"✅ Summarized image pages: {summarized_count}")
        
        # 5. Check chunks by type
        cursor.execute("""
            SELECT chunk_type, COUNT(*) as count, 
                   SUM(CASE WHEN vector_embedded = 1 THEN 1 ELSE 0 END) as embedded_count
            FROM pdf_chunks 
            GROUP BY chunk_type
        """)
        chunk_stats = cursor.fetchall()
        
        print(f"\n🔗 CHUNK STATISTICS:")
        total_chunks = 0
        total_embedded = 0
        
        for chunk_type, count, embedded_count in chunk_stats:
            print(f"  {chunk_type or 'unknown'}: {embedded_count}/{count} chunks embedded")
            total_chunks += count
            total_embedded += embedded_count
        
        print(f"  📊 TOTAL: {total_embedded}/{total_chunks} chunks embedded")
        
        # 6. Check chunks by camera type
        cursor.execute("""
            SELECT camera_type, chunk_type, COUNT(*) as count, 
                   SUM(CASE WHEN vector_embedded = 1 THEN 1 ELSE 0 END) as embedded_count
            FROM pdf_chunks 
            WHERE camera_type IS NOT NULL
            GROUP BY camera_type, chunk_type
            ORDER BY camera_type, chunk_type
        """)
        camera_chunk_stats = cursor.fetchall()
        
        if camera_chunk_stats:
            print(f"\n📷 CAMERA TYPE & CHUNK TYPE BREAKDOWN:")
            for camera_type, chunk_type, count, embedded_count in camera_chunk_stats:
                print(f"  {camera_type} - {chunk_type or 'unknown'}: {embedded_count}/{count} embedded")
        
        # 7. Check recent files
        cursor.execute("""
            SELECT file_name, camera_type, last_modified 
            FROM pdf_files 
            WHERE file_name NOT LIKE '%_summary.pdf'
            ORDER BY last_modified DESC 
            LIMIT 5
        """)
        recent_files = cursor.fetchall()
        
        if recent_files:
            print(f"\n📅 RECENT ORIGINAL FILES:")
            for file_name, camera_type, last_modified in recent_files:
                print(f"  {file_name} ({camera_type}) - {last_modified}")
        
        # 8. Check recent summary files
        cursor.execute("""
            SELECT file_name, last_modified 
            FROM pdf_files 
            WHERE file_name LIKE '%_summary.pdf'
            ORDER BY last_modified DESC 
            LIMIT 5
        """)
        recent_summaries = cursor.fetchall()
        
        if recent_summaries:
            print(f"\n📋 RECENT SUMMARY FILES:")
            for file_name, last_modified in recent_summaries:
                print(f"  {file_name} - {last_modified}")
        
        # 9. Pipeline readiness check
        print(f"\n🚦 PIPELINE READINESS:")
        
        if unsummarized_count > 0:
            print(f"  🔄 Ready for Step 1 (img2.py): {unsummarized_count} image pages to process")
        else:
            print(f"  ✅ Step 1 (img2.py): All image pages processed")
        
        unembedded_chunks = total_chunks - total_embedded
        if unembedded_chunks > 0:
            print(f"  🔄 Ready for Step 3 (vector_embedding.py): {unembedded_chunks} chunks to embed")
        else:
            print(f"  ✅ Step 3 (vector_embedding.py): All chunks embedded")
        
        print(f"  🔄 Step 2 (enhanced_chunking.py): Always ready to process new content")
        
        print(f"\n🎯 NEXT ACTIONS:")
        if unsummarized_count > 0:
            print(f"  1. Run img2.py to process {unsummarized_count} image pages")
            print(f"  2. Run enhanced_chunking.py to chunk all PDFs")
            print(f"  3. Run vector_embedding.py to embed chunks")
        elif unembedded_chunks > 0:
            print(f"  1. Run enhanced_chunking.py to chunk any new PDFs")
            print(f"  2. Run vector_embedding.py to embed {unembedded_chunks} chunks")
        else:
            print(f"  ✅ Pipeline is up to date! Ready for new uploads.")
            
    except Exception as e:
        print(f"❌ Error checking pipeline status: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    check_pipeline_status()
