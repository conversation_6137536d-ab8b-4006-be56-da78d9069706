#!/usr/bin/env python3
"""
Add chunk_type column to pdf_chunks table for distinguishing original vs summary chunks
"""

import mysql.connector

# Database config
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': 'vishal2004',
    'database': 'rough'
}

def add_chunk_type_column():
    """Add chunk_type column to pdf_chunks table if it doesn't exist."""
    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()
        
        # Check if column already exists
        cursor.execute("""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'rough' 
            AND TABLE_NAME = 'pdf_chunks' 
            AND COLUMN_NAME = 'chunk_type'
        """)
        
        column_exists = cursor.fetchone()[0] > 0
        
        if not column_exists:
            # Add the chunk_type column
            cursor.execute("""
                ALTER TABLE pdf_chunks 
                ADD COLUMN chunk_type VARCHAR(20) DEFAULT 'original' NULL
            """)
            conn.commit()
            print("✅ Added chunk_type column to pdf_chunks table")
            
            # Update existing chunks to have 'original' type
            cursor.execute("""
                UPDATE pdf_chunks 
                SET chunk_type = 'original' 
                WHERE chunk_type IS NULL
            """)
            conn.commit()
            print("✅ Updated existing chunks to have 'original' chunk_type")
        else:
            print("✅ chunk_type column already exists in pdf_chunks table")
            
        # Show current table structure
        cursor.execute("DESCRIBE pdf_chunks")
        columns = cursor.fetchall()
        
        print("\n📋 Current pdf_chunks table structure:")
        for column in columns:
            print(f"  {column[0]} - {column[1]} - {column[2]} - {column[3]} - {column[4]} - {column[5]}")
            
    except Exception as e:
        print(f"❌ Error adding chunk_type column: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    add_chunk_type_column()
